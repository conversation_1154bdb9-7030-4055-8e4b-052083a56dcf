<?php
/**
 * WhatsApp Order Button Admin Class
 *
 * @package WhatsApp_Order_Button
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * WhatsApp Order Button Admin Class
 */
class WhatsApp_Order_Button_Admin {

    /**
     * Single instance of the class
     *
     * @var WhatsApp_Order_Button_Admin
     */
    private static $instance = null;

    /**
     * Get single instance of the class
     *
     * @return WhatsApp_Order_Button_Admin
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'register_settings'));
        add_action('woocommerce_product_options_general_product_data', array($this, 'add_product_fields'));
        add_action('woocommerce_process_product_meta', array($this, 'save_product_fields'));
        add_action('product_cat_add_form_fields', array($this, 'add_category_fields'));
        add_action('product_cat_edit_form_fields', array($this, 'edit_category_fields'));
        add_action('edited_product_cat', array($this, 'save_category_fields'));
        add_action('create_product_cat', array($this, 'save_category_fields'));
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        // Add as submenu under WooCommerce (existing)
        add_submenu_page(
            'woocommerce',
            __('WhatsApp Order Settings', 'whatsapp-order-button'),
            __('WhatsApp Order', 'whatsapp-order-button'),
            'manage_woocommerce',
            'whatsapp-order-settings',
            array($this, 'admin_page')
        );

        // Add as main menu item in sidebar for easier access
        add_menu_page(
            __('WhatsApp Order Settings', 'whatsapp-order-button'),
            __('WhatsApp Order', 'whatsapp-order-button'),
            'manage_woocommerce',
            'whatsapp-order-main',
            array($this, 'admin_page'),
            'data:image/svg+xml;base64,' . base64_encode('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.486z"/></svg>'),
            30
        );
    }

    /**
     * Register settings
     */
    public function register_settings() {
        register_setting('whatsapp_order_button_settings', 'whatsapp_order_button_settings');
    }

    /**
     * Admin page content
     */
    public function admin_page() {
        if (isset($_POST['submit'])) {
            $this->save_settings();
        }

        $settings = WhatsApp_Order_Button::get_instance()->get_settings();

        // Extract specific variables for the template
        $button_style = isset($settings['button_style']) ? $settings['button_style'] : 'modern';
        $custom_button_image = isset($settings['custom_button_image']) ? $settings['custom_button_image'] : '';
        $show_on_shop = isset($settings['show_on_shop']) ? $settings['show_on_shop'] : 'no';

        include WHATSAPP_ORDER_BUTTON_PLUGIN_DIR . 'admin/admin-settings.php';
    }

    /**
     * Save settings
     */
    private function save_settings() {
        if (!wp_verify_nonce($_POST['whatsapp_order_nonce'], 'whatsapp_order_settings')) {
            wp_die(__('Security check failed', 'whatsapp-order-button'));
        }

        $settings = array(
            'enabled' => sanitize_text_field(isset($_POST['enabled']) ? $_POST['enabled'] : 'no'),
            'whatsapp_number' => sanitize_text_field(isset($_POST['whatsapp_number']) ? $_POST['whatsapp_number'] : ''),
            'button_text' => sanitize_text_field(isset($_POST['button_text']) ? $_POST['button_text'] : __('Order on WhatsApp', 'whatsapp-order-button')),
            'button_position' => sanitize_text_field(isset($_POST['button_position']) ? $_POST['button_position'] : 'after_add_to_cart'),
            'disable_add_to_cart' => sanitize_text_field(isset($_POST['disable_add_to_cart']) ? $_POST['disable_add_to_cart'] : 'no'),
            'message_template' => wp_kses_post(isset($_POST['message_template']) ? $_POST['message_template'] : ''),
            'button_color' => sanitize_hex_color(isset($_POST['button_color']) ? $_POST['button_color'] : '#25D366'),
            'button_text_color' => sanitize_hex_color(isset($_POST['button_text_color']) ? $_POST['button_text_color'] : '#ffffff'),
            'button_size' => sanitize_text_field(isset($_POST['button_size']) ? $_POST['button_size'] : 'medium'),
            'button_style' => sanitize_text_field(isset($_POST['button_style']) ? $_POST['button_style'] : 'modern'),
            'custom_button_image' => esc_url_raw(isset($_POST['custom_button_image']) ? $_POST['custom_button_image'] : ''),
            'show_on_shop' => sanitize_text_field(isset($_POST['show_on_shop']) ? $_POST['show_on_shop'] : 'no'),
            'show_on_cart' => sanitize_text_field(isset($_POST['show_on_cart']) ? $_POST['show_on_cart'] : 'yes'),
            'show_on_checkout' => sanitize_text_field(isset($_POST['show_on_checkout']) ? $_POST['show_on_checkout'] : 'yes'),
            'replace_checkout_button' => sanitize_text_field(isset($_POST['replace_checkout_button']) ? $_POST['replace_checkout_button'] : 'no'),
            'device_behavior' => sanitize_text_field(isset($_POST['device_behavior']) ? $_POST['device_behavior'] : 'auto'),
            'enable_analytics' => sanitize_text_field(isset($_POST['enable_analytics']) ? $_POST['enable_analytics'] : 'no'),
            'floating_button' => sanitize_text_field(isset($_POST['floating_button']) ? $_POST['floating_button'] : 'no'),
            'floating_position' => sanitize_text_field(isset($_POST['floating_position']) ? $_POST['floating_position'] : 'bottom-right'),
            'show_size_guide' => sanitize_text_field(isset($_POST['show_size_guide']) ? $_POST['show_size_guide'] : 'no'),
            'size_guide_text' => sanitize_text_field(isset($_POST['size_guide_text']) ? $_POST['size_guide_text'] : __('Size Guide', 'whatsapp-order-button')),
            'custom_css' => wp_strip_all_tags(isset($_POST['custom_css']) ? $_POST['custom_css'] : ''),
            'exclude_categories' => array_map('intval', isset($_POST['exclude_categories']) ? $_POST['exclude_categories'] : array()),
            'exclude_products' => array_map('intval', isset($_POST['exclude_products']) ? $_POST['exclude_products'] : array()),
            // Multiple numbers settings
            'enable_multiple_numbers' => sanitize_text_field(isset($_POST['enable_multiple_numbers']) ? $_POST['enable_multiple_numbers'] : 'no'),
            'phone_numbers' => $this->sanitize_phone_numbers(isset($_POST['phone_numbers']) ? $_POST['phone_numbers'] : array()),
            'default_phone_number' => intval(isset($_POST['default_phone_number']) ? $_POST['default_phone_number'] : 0),
            'multiple_numbers_style' => sanitize_text_field(isset($_POST['multiple_numbers_style']) ? $_POST['multiple_numbers_style'] : 'dropdown'),
            'multiple_numbers_text' => sanitize_text_field(isset($_POST['multiple_numbers_text']) ? $_POST['multiple_numbers_text'] : __('Choose Department', 'whatsapp-order-button')),
            // New URL customization settings
            'enable_custom_urls' => sanitize_text_field(isset($_POST['enable_custom_urls']) ? $_POST['enable_custom_urls'] : 'no'),
            'custom_url_template' => sanitize_textarea_field(isset($_POST['custom_url_template']) ? $_POST['custom_url_template'] : ''),
            'custom_url_fallback' => sanitize_text_field(isset($_POST['custom_url_fallback']) ? $_POST['custom_url_fallback'] : 'whatsapp')
        );

        WhatsApp_Order_Button::get_instance()->update_settings($settings);

        add_action('admin_notices', function() {
            echo '<div class="notice notice-success is-dismissible"><p>' . __('Settings saved successfully!', 'whatsapp-order-button') . '</p></div>';
        });
    }

    /**
     * Sanitize phone numbers array
     */
    private function sanitize_phone_numbers($phone_numbers) {
        if (!is_array($phone_numbers)) {
            return array();
        }

        $sanitized = array();
        foreach ($phone_numbers as $phone_data) {
            if (is_array($phone_data)) {
                $sanitized[] = array(
                    'label' => sanitize_text_field(isset($phone_data['label']) ? $phone_data['label'] : ''),
                    'number' => sanitize_text_field(isset($phone_data['number']) ? $phone_data['number'] : ''),
                    'default' => false // Will be set based on default_phone_number index
                );
            }
        }

        // Set the default phone number
        $default_index = intval(isset($_POST['default_phone_number']) ? $_POST['default_phone_number'] : 0);
        if (isset($sanitized[$default_index])) {
            $sanitized[$default_index]['default'] = true;
        }

        return $sanitized;
    }

    /**
     * Add product fields
     */
    public function add_product_fields() {
        global $post;

        echo '<div class="options_group">';
        
        woocommerce_wp_text_input(array(
            'id' => '_whatsapp_number',
            'label' => __('WhatsApp Number', 'whatsapp-order-button'),
            'description' => __('Override global WhatsApp number for this product', 'whatsapp-order-button'),
            'desc_tip' => true,
            'placeholder' => '+1234567890'
        ));

        woocommerce_wp_checkbox(array(
            'id' => '_disable_add_to_cart',
            'label' => __('Disable Add to Cart', 'whatsapp-order-button'),
            'description' => __('Disable add to cart for this product', 'whatsapp-order-button')
        ));

        woocommerce_wp_checkbox(array(
            'id' => '_hide_whatsapp_button',
            'label' => __('Hide WhatsApp Button', 'whatsapp-order-button'),
            'description' => __('Hide WhatsApp button for this product', 'whatsapp-order-button')
        ));

        woocommerce_wp_textarea_input(array(
            'id' => '_custom_whatsapp_url',
            'label' => __('Custom WhatsApp URL', 'whatsapp-order-button'),
            'description' => __('Custom URL template for this product. Use placeholders like {product_name}, {price}, etc.', 'whatsapp-order-button'),
            'desc_tip' => true,
            'placeholder' => 'https://wa.me/{whatsapp_number}?text={encoded_message}',
            'rows' => 3
        ));

        echo '</div>';
    }

    /**
     * Save product fields
     */
    public function save_product_fields($post_id) {
        $whatsapp_number = sanitize_text_field(isset($_POST['_whatsapp_number']) ? $_POST['_whatsapp_number'] : '');
        $disable_add_to_cart = isset($_POST['_disable_add_to_cart']) ? 'yes' : 'no';
        $hide_whatsapp_button = isset($_POST['_hide_whatsapp_button']) ? 'yes' : 'no';
        $custom_whatsapp_url = sanitize_textarea_field(isset($_POST['_custom_whatsapp_url']) ? $_POST['_custom_whatsapp_url'] : '');

        update_post_meta($post_id, '_whatsapp_number', $whatsapp_number);
        update_post_meta($post_id, '_disable_add_to_cart', $disable_add_to_cart);
        update_post_meta($post_id, '_hide_whatsapp_button', $hide_whatsapp_button);
        update_post_meta($post_id, '_custom_whatsapp_url', $custom_whatsapp_url);
    }

    /**
     * Add category fields
     */
    public function add_category_fields() {
        ?>
        <div class="form-field">
            <label for="whatsapp_number"><?php _e('WhatsApp Number', 'whatsapp-order-button'); ?></label>
            <input type="text" name="whatsapp_number" id="whatsapp_number" placeholder="+1234567890" />
            <p class="description"><?php _e('Override global WhatsApp number for this category', 'whatsapp-order-button'); ?></p>
        </div>
        <div class="form-field">
            <label for="disable_add_to_cart">
                <input type="checkbox" name="disable_add_to_cart" id="disable_add_to_cart" value="yes" />
                <?php _e('Disable Add to Cart for this category', 'whatsapp-order-button'); ?>
            </label>
        </div>
        <?php
    }

    /**
     * Edit category fields
     */
    public function edit_category_fields($term) {
        $whatsapp_number = get_term_meta($term->term_id, 'whatsapp_number', true);
        $disable_add_to_cart = get_term_meta($term->term_id, 'disable_add_to_cart', true);
        ?>
        <tr class="form-field">
            <th scope="row" valign="top"><label for="whatsapp_number"><?php _e('WhatsApp Number', 'whatsapp-order-button'); ?></label></th>
            <td>
                <input type="text" name="whatsapp_number" id="whatsapp_number" value="<?php echo esc_attr($whatsapp_number); ?>" placeholder="+1234567890" />
                <p class="description"><?php _e('Override global WhatsApp number for this category', 'whatsapp-order-button'); ?></p>
            </td>
        </tr>
        <tr class="form-field">
            <th scope="row" valign="top"><label for="disable_add_to_cart"><?php _e('Disable Add to Cart', 'whatsapp-order-button'); ?></label></th>
            <td>
                <label for="disable_add_to_cart">
                    <input type="checkbox" name="disable_add_to_cart" id="disable_add_to_cart" value="yes" <?php checked($disable_add_to_cart, 'yes'); ?> />
                    <?php _e('Disable Add to Cart for this category', 'whatsapp-order-button'); ?>
                </label>
            </td>
        </tr>
        <?php
    }

    /**
     * Save category fields
     */
    public function save_category_fields($term_id) {
        $whatsapp_number = sanitize_text_field(isset($_POST['whatsapp_number']) ? $_POST['whatsapp_number'] : '');
        $disable_add_to_cart = isset($_POST['disable_add_to_cart']) ? 'yes' : 'no';

        update_term_meta($term_id, 'whatsapp_number', $whatsapp_number);
        update_term_meta($term_id, 'disable_add_to_cart', $disable_add_to_cart);
    }
}
